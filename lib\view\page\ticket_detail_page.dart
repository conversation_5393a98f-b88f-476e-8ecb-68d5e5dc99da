import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:qr_flutter/qr_flutter.dart';
import '../../models/ticket_model.dart';

class TicketDetailPage extends StatelessWidget {
  final Ticket ticket;

  const TicketDetailPage({Key? key, required this.ticket}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xff1a1a1a),
      body: SafeArea(
        child: Column(
          children: [
            // Header
            _buildHeader(),

            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    // Ticket Card
                    _buildTicketCard(),

                    const SizedBox(height: 24),

                    // QR Code Section
                    _buildQRCodeSection(),

                    const SizedBox(height: 24),

                    // Additional Info
                    _buildAdditionalInfo(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xff4E4376), Color(0xff2B5876)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Get.back(),
            icon: const Icon(
              Icons.arrow_back,
              color: Colors.white,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Chi Tiết Vé',
              style: GoogleFonts.mulish(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: _getStatusColor(ticket.status),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              _getStatusText(ticket.status),
              style: GoogleFonts.mulish(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTicketCard() {
    final dateFormat = DateFormat('EEEE, dd/MM/yyyy', 'vi_VN');
    final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');

    return Container(
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xff2B5876), Color(0xff4E4376)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          // Movie Info Section
          Container(
            padding: const EdgeInsets.all(20),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Movie Poster
                ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: _buildPosterImage(),
                ),

                const SizedBox(width: 16),

                // Movie Details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        ticket.movieTitle,
                        style: GoogleFonts.mulish(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 12),
                      _buildInfoRow(
                        Icons.calendar_today,
                        'Ngày chiếu',
                        dateFormat.format(DateTime.parse(ticket.date)),
                      ),
                      const SizedBox(height: 8),
                      _buildInfoRow(
                        Icons.access_time,
                        'Giờ chiếu',
                        ticket.time,
                      ),
                      const SizedBox(height: 8),
                      _buildInfoRow(
                        Icons.location_on,
                        'Rạp chiếu',
                        ticket.theaterName,
                      ),
                      const SizedBox(height: 8),
                      _buildInfoRow(
                        Icons.tv,
                        'Phòng chiếu',
                        ticket.screenName,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Divider with dotted line effect
          Container(
            height: 1,
            margin: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: List.generate(
                50,
                (index) => Expanded(
                  child: Container(
                    height: 1,
                    color: index % 2 == 0 ? Colors.white30 : Colors.transparent,
                  ),
                ),
              ),
            ),
          ),

          // Seat and Price Section
          Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoRow(
                        Icons.event_seat,
                        'Ghế ngồi',
                        _formatSeats(ticket.seats),
                      ),
                    ),
                    Expanded(
                      child: _buildInfoRow(
                        Icons.attach_money,
                        'Tổng tiền',
                        currencyFormat.format(ticket.finalPrice),
                        valueColor: Colors.greenAccent,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                _buildInfoRow(
                  Icons.payment,
                  'Phương thức thanh toán',
                  _getPaymentMethodText(ticket.paymentMethod),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQRCodeSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            'Mã QR Vé',
            style: GoogleFonts.mulish(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),

          const SizedBox(height: 16),

          // QR Code
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: QrImageView(
              data: _generateQRData(),
              version: QrVersions.auto,
              size: 200.0,
              backgroundColor: Colors.white,
              eyeStyle: const QrEyeStyle(
                eyeShape: QrEyeShape.square,
                color: Colors.black,
              ),
              dataModuleStyle: const QrDataModuleStyle(
                dataModuleShape: QrDataModuleShape.square,
                color: Colors.black,
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Booking Code
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Mã đặt vé: ',
                  style: GoogleFonts.mulish(
                    fontSize: 14,
                    color: Colors.black54,
                  ),
                ),
                Text(
                  ticket.bookingCode,
                  style: GoogleFonts.robotoMono(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(width: 8),
                GestureDetector(
                  onTap: () {
                    Clipboard.setData(ClipboardData(text: ticket.bookingCode));
                    Get.snackbar(
                      'Đã sao chép',
                      'Mã đặt vé đã được sao chép vào clipboard',
                      snackPosition: SnackPosition.BOTTOM,
                      backgroundColor: Colors.green,
                      colorText: Colors.white,
                      duration: const Duration(seconds: 2),
                    );
                  },
                  child: const Icon(
                    Icons.copy,
                    size: 16,
                    color: Colors.blue,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 12),

          Text(
            'Vui lòng xuất trình mã QR này tại quầy vé',
            style: GoogleFonts.mulish(
              fontSize: 12,
              color: Colors.black54,
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalInfo() {
    final purchaseFormat = DateFormat('dd/MM/yyyy HH:mm', 'vi_VN');

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Thông Tin Bổ Sung',
            style: GoogleFonts.mulish(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 12),
          _buildInfoRow(
            Icons.shopping_cart,
            'Ngày mua vé',
            purchaseFormat.format(ticket.purchaseDate),
          ),
          if (ticket.discountApplied > 0) ...[
            const SizedBox(height: 8),
            _buildInfoRow(
              Icons.discount,
              'Giảm giá',
              NumberFormat.currency(locale: 'vi_VN', symbol: '₫')
                  .format(ticket.discountApplied),
              valueColor: Colors.orange,
            ),
          ],
          if (ticket.loyaltyPointsEarned > 0) ...[
            const SizedBox(height: 8),
            _buildInfoRow(
              Icons.stars,
              'Điểm tích lũy',
              '${ticket.loyaltyPointsEarned} điểm',
              valueColor: Colors.amber,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value,
      {Color? valueColor}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 16,
          color: Colors.white70,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: GoogleFonts.mulish(
                  fontSize: 12,
                  color: Colors.white60,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: GoogleFonts.mulish(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: valueColor ?? Colors.white,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPosterImage() {
    final posterUrl = _getCorrectPosterUrl(ticket.moviePosterPath);

    if (posterUrl == null) {
      return Container(
        height: 120,
        width: 80,
        decoration: BoxDecoration(
          color: Colors.grey[800],
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Center(
          child: Icon(
            Icons.movie,
            color: Colors.white54,
            size: 40,
          ),
        ),
      );
    }

    return Image.network(
      posterUrl,
      height: 120,
      width: 80,
      fit: BoxFit.cover,
      errorBuilder: (context, error, stackTrace) {
        return Container(
          height: 120,
          width: 80,
          decoration: BoxDecoration(
            color: Colors.grey[800],
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Center(
            child: Icon(
              Icons.movie,
              color: Colors.white54,
              size: 40,
            ),
          ),
        );
      },
    );
  }

  String? _getCorrectPosterUrl(String? posterPath) {
    if (posterPath == null || posterPath.isEmpty) {
      return null;
    }

    // If it's already a full URL, use it directly
    if (posterPath.startsWith('http')) {
      return posterPath;
    }

    // If it's a TMDB path (starts with /), construct the full URL
    if (posterPath.startsWith('/')) {
      return 'https://image.tmdb.org/t/p/w300$posterPath';
    }

    // Fallback to null to use errorBuilder
    return null;
  }

  String _formatSeats(List<TicketSeat> seats) {
    if (seats.isEmpty) return 'N/A';
    if (seats.length == 1) return seats.first.seatId;

    // Group seats by row
    Map<String, List<String>> seatsByRow = {};
    for (var seat in seats) {
      if (!seatsByRow.containsKey(seat.row)) {
        seatsByRow[seat.row] = [];
      }
      seatsByRow[seat.row]!.add(seat.number);
    }

    // Format seats by row
    List<String> formattedSeats = [];
    seatsByRow.forEach((row, numbers) {
      numbers.sort();
      formattedSeats.add('$row${numbers.join(', $row')}');
    });

    return formattedSeats.join(', ');
  }

  String _getStatusText(TicketStatus status) {
    switch (status) {
      case TicketStatus.confirmed:
        return 'Đã xác nhận';
      case TicketStatus.used:
        return 'Đã sử dụng';
      case TicketStatus.cancelled:
        return 'Đã hủy';
      case TicketStatus.expired:
        return 'Hết hạn';
    }
  }

  Color _getStatusColor(TicketStatus status) {
    switch (status) {
      case TicketStatus.confirmed:
        return Colors.green;
      case TicketStatus.used:
        return Colors.blue;
      case TicketStatus.cancelled:
        return Colors.red;
      case TicketStatus.expired:
        return Colors.orange;
    }
  }

  String _getPaymentMethodText(String paymentMethod) {
    switch (paymentMethod.toLowerCase()) {
      case 'credit_card':
        return 'Thẻ tín dụng';
      case 'debit_card':
        return 'Thẻ ghi nợ';
      case 'e_wallet':
        return 'Ví điện tử';
      case 'paypal':
        return 'PayPal';
      case 'cash':
        return 'Tiền mặt';
      default:
        return paymentMethod;
    }
  }

  String _generateQRData() {
    // Generate QR data with ticket information
    return 'TICKET:${ticket.bookingCode}|MOVIE:${ticket.movieTitle}|DATE:${ticket.date}|TIME:${ticket.time}|THEATER:${ticket.theaterName}|SCREEN:${ticket.screenName}|SEATS:${_formatSeats(ticket.seats)}|PRICE:${ticket.finalPrice}';
  }
}
